import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'optimagic_', 'src'))

try:
    import numpy as np
    from numpy.testing import assert_array_almost_equal as aaae

    from optimagic import minimize
    from optimagic.optimization.optimize import Bounds
    from optimagic.optimizers.pygad_new import (
        Pygad,
        RandomMutation,
        SwapMutation,
        AdaptiveMutation,
    )
    DEPS_AVAILABLE = True
except ImportError as e:
    print(f"Dependencies not available: {e}")
    DEPS_AVAILABLE = False


def square_function(x):
    return (x**2).sum()


def test_pygad_with_string_mutation():
    if not DEPS_AVAILABLE:
        print("Skipping test - dependencies not available")
        return
    result = minimize(
        fun=square_function,
        params=np.array([1.0, 2.0]),
        algorithm=Pygad(
            num_generations=20,
            population_size=10,
            mutation="random",
            seed=42
        ),
        bounds=Bounds(
            lower=np.array([-5.0, -5.0]),
            upper=np.array([5.0, 5.0])
        )
    )
    aaae(result.params, np.array([0.0, 0.0]), decimal=1)


def test_pygad_with_class_mutation():
    if not DEPS_AVAILABLE:
        print("Skipping test - dependencies not available")
        return
    result = minimize(
        fun=square_function,
        params=np.array([1.0, 2.0]),
        algorithm=Pygad(
            num_generations=20,
            population_size=10,
            mutation=RandomMutation,
            seed=42
        ),
        bounds=Bounds(
            lower=np.array([-5.0, -5.0]),
            upper=np.array([5.0, 5.0])
        )
    )
    aaae(result.params, np.array([0.0, 0.0]), decimal=1)


def test_pygad_with_instance_mutation():
    if not DEPS_AVAILABLE:
        print("Skipping test - dependencies not available")
        return
    result = minimize(
        fun=square_function,
        params=np.array([1.0, 2.0]),
        algorithm=Pygad(
            num_generations=20,
            population_size=10,
            mutation=RandomMutation(probability=0.1),
            seed=42
        ),
        bounds=Bounds(
            lower=np.array([-5.0, -5.0]),
            upper=np.array([5.0, 5.0])
        )
    )
    aaae(result.params, np.array([0.0, 0.0]), decimal=1)


# def test_pygad_with_swap_mutation():
#     result = minimize(
#         fun=square_function,
#         params=np.array([1.0, 2.0]),
#         algorithm=Pygad(
#             num_generations=20,
#             population_size=10,
#             mutation=SwapMutation(),
#             seed=42
#         ),
#         bounds=Bounds(
#             lower=np.array([-5.0, -5.0]),
#             upper=np.array([5.0, 5.0])
#         )
#     )
#     aaae(result.params, np.array([0.0, 0.0]), decimal=1)


# def test_pygad_with_adaptive_mutation():
#     result = minimize(
#         fun=square_function,
#         params=np.array([1.0, 2.0]),
#         algorithm=Pygad(
#             num_generations=20,
#             population_size=10,
#             mutation=AdaptiveMutation(probability_bad=0.2, probability_good=0.05),
#             seed=42
#         ),
#         bounds=Bounds(
#             lower=np.array([-5.0, -5.0]),
#             upper=np.array([5.0, 5.0])
#         )
#     )
#     aaae(result.params, np.array([0.0, 0.0]), decimal=1)


if __name__ == "__main__":
    print("Running PyGAD optimizer tests...")

    if not DEPS_AVAILABLE:
        print("Cannot run tests - missing dependencies (numpy, optimagic)")
        print("This test file demonstrates the structure for testing PyGAD optimizer")
        print("with different mutation configurations:")
        print("1. String mutation: mutation='random'")
        print("2. Class mutation: mutation=RandomMutation")
        print("3. Instance mutation: mutation=RandomMutation(probability=0.1)")
        sys.exit(0)

    print("Test 1: String mutation")
    test_pygad_with_string_mutation()
    print("✓ Passed")

    print("Test 2: Class mutation")
    test_pygad_with_class_mutation()
    print("✓ Passed")

    print("Test 3: Instance mutation")
    test_pygad_with_instance_mutation()
    print("✓ Passed")

    print("All tests passed!")
